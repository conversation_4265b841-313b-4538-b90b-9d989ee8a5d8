using System;
using System.Text.Json;
using System.Text.Json.Serialization;
using cAlgo.API;

namespace cAlgo.Robots
{
    /// <summary>
    /// Trading signal data model from AgentServer API
    /// </summary>
    public class TradingSignal
    {
        [JsonPropertyName("symbol")]
        public string Symbol { get; set; }

        [JsonPropertyName("signal")]
        public string Signal { get; set; } // BUY, SELL, HOLD

        [JsonPropertyName("confidence")]
        public int Confidence { get; set; } // 1-10 scale

        [JsonPropertyName("entryPrice")]
        public double EntryPrice { get; set; }

        [JsonPropertyName("targetPrice")]
        public double TargetPrice { get; set; }

        [JsonPropertyName("stopLoss")]
        public double StopLoss { get; set; }

        [JsonPropertyName("riskReward")]
        public double RiskReward { get; set; }

        [JsonPropertyName("position")]
        public string Position { get; set; } // LONG, SHORT, NEUTRAL

        [JsonPropertyName("timeframe")]
        public string Timeframe { get; set; } // SWING, DAY, SCALP

        [JsonPropertyName("strength")]
        public string Strength { get; set; } // STRONG, MEDIUM, WEAK

        [JsonPropertyName("trend")]
        public string Trend { get; set; } // BULLISH, BEARISH, NEUTRAL

        [JsonPropertyName("support")]
        public double Support { get; set; }

        [JsonPropertyName("resistance")]
        public double Resistance { get; set; }

        [JsonPropertyName("momentum")]
        public string Momentum { get; set; } // BULLISH, BEARISH, NEUTRAL

        [JsonPropertyName("volume")]
        public string Volume { get; set; } // HIGH, MEDIUM, LOW

        [JsonPropertyName("volatility")]
        public string Volatility { get; set; } // HIGH, MEDIUM, LOW

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Checks if the signal is actionable (BUY or SELL with valid prices)
        /// </summary>
        public bool IsActionable =>
            (Signal == "BUY" || Signal == "SELL") &&
            EntryPrice > 0 &&
            StopLoss > 0 &&
            TargetPrice > 0;

        /// <summary>
        /// Gets the trade type based on the signal
        /// </summary>
        public TradeType? GetTradeType()
        {
            return Signal switch
            {
                "BUY" => TradeType.Buy,
                "SELL" => TradeType.Sell,
                _ => null
            };
        }
    }

    [Robot(AccessRights = AccessRights.None, AddIndicators = true)]
    public class TradingAgent : Robot
    {
        [Parameter("API Base URL", DefaultValue = "http://tradingagent.undeclab.com")]
        public string ApiBaseUrl { get; set; }

        [Parameter("Signal Check Interval (seconds)", DefaultValue = 30, MinValue = 10, MaxValue = 300)]
        public int SignalCheckInterval { get; set; }

        [Parameter("Minimum Confidence Level", DefaultValue = 6, MinValue = 1, MaxValue = 10)]
        public int MinConfidenceLevel { get; set; }

        [Parameter("Risk Per Trade (%)", DefaultValue = 2.0, MinValue = 0.1, MaxValue = 10.0)]
        public double RiskPercentage { get; set; }

        [Parameter("Max Open Positions", DefaultValue = 3, MinValue = 1, MaxValue = 10)]
        public int MaxOpenPositions { get; set; }

        [Parameter("Enable Trading", DefaultValue = false)]
        public bool EnableTrading { get; set; }

        private Timer _signalTimer;
        private TradingSignal _lastSignal;
        private readonly object _lockObject = new object();

        protected override void OnStart()
        {
            Print($"TradingAgent started for {SymbolName}");
            Print($"API URL: {ApiBaseUrl}");
            Print($"Signal Check Interval: {SignalCheckInterval} seconds");
            Print($"Min Confidence: {MinConfidenceLevel}");
            Print($"Risk Per Trade: {RiskPercentage}%");
            Print($"Trading Enabled: {EnableTrading}");

            // Start the signal checking timer
            _signalTimer = Timer.Start(SignalCheckInterval, CheckForSignals);

            // Initial signal check
            CheckForSignals();
        }

        protected override void OnTick()
        {
            // Handle price updates here if needed
        }

        protected override void OnStop()
        {
            _signalTimer?.Stop();
            Print("TradingAgent stopped");
        }

        /// <summary>
        /// Checks for new trading signals from the AgentServer API
        /// </summary>
        private void CheckForSignals()
        {
            try
            {
                var apiUrl = $"{ApiBaseUrl}/analyze/ctrader/{SymbolName}?format=summary";
                Print($"Checking signals from: {apiUrl}");

                var response = Http.Get(apiUrl);

                if (response.IsSuccessful)
                {
                    var signal = JsonSerializer.Deserialize<TradingSignal>(response.Body);
                    ProcessSignal(signal);
                }
                else
                {
                    Print($"Failed to get signal: HTTP {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                Print($"Error checking signals: {ex.Message}");
            }
        }

        /// <summary>
        /// Processes the received trading signal and executes trades if conditions are met
        /// </summary>
        private void ProcessSignal(TradingSignal signal)
        {
            if (signal == null)
            {
                Print("Received null signal");
                return;
            }

            lock (_lockObject)
            {
                Print($"Signal: {signal.Signal} | Confidence: {signal.Confidence} | Entry: {signal.EntryPrice} | Target: {signal.TargetPrice} | Stop: {signal.StopLoss}");

                // Check if signal has changed significantly
                if (HasSignalChanged(signal))
                {
                    _lastSignal = signal;

                    // Only process actionable signals with sufficient confidence
                    if (signal.IsActionable && signal.Confidence >= MinConfidenceLevel)
                    {
                        if (EnableTrading)
                        {
                            ExecuteTrade(signal);
                        }
                        else
                        {
                            Print($"Trading disabled - would execute {signal.Signal} trade");
                        }
                    }
                    else if (!signal.IsActionable)
                    {
                        Print($"Signal not actionable: {signal.Signal}");

                        // Close existing positions if signal is HOLD or not actionable
                        if (signal.Signal == "HOLD")
                        {
                            CloseExistingPositions("Signal changed to HOLD");
                        }
                    }
                    else
                    {
                        Print($"Signal confidence too low: {signal.Confidence} < {MinConfidenceLevel}");
                    }
                }
            }
        }

        /// <summary>
        /// Checks if the signal has changed significantly from the last one
        /// </summary>
        private bool HasSignalChanged(TradingSignal newSignal)
        {
            if (_lastSignal == null) return true;

            return _lastSignal.Signal != newSignal.Signal ||
                   Math.Abs(_lastSignal.EntryPrice - newSignal.EntryPrice) > 0.0001 ||
                   Math.Abs(_lastSignal.StopLoss - newSignal.StopLoss) > 0.0001 ||
                   Math.Abs(_lastSignal.TargetPrice - newSignal.TargetPrice) > 0.0001 ||
                   _lastSignal.Confidence != newSignal.Confidence;
        }

        /// <summary>
        /// Executes a trade based on the signal
        /// </summary>
        private void ExecuteTrade(TradingSignal signal)
        {
            try
            {
                // Check if we already have too many open positions
                if (Positions.Count >= MaxOpenPositions)
                {
                    Print($"Max positions reached ({MaxOpenPositions}). Skipping trade.");
                    return;
                }

                var tradeType = signal.GetTradeType();
                if (!tradeType.HasValue)
                {
                    Print($"Invalid trade type for signal: {signal.Signal}");
                    return;
                }

                // Calculate position size based on risk percentage
                var volume = CalculatePositionSize(signal);
                if (volume <= 0)
                {
                    Print("Calculated volume is zero or negative. Skipping trade.");
                    return;
                }

                // Close any existing positions for this symbol first
                CloseExistingPositions("New signal received");

                // Execute the trade
                var result = ExecuteMarketOrder(tradeType.Value, SymbolName, volume, "AgentServer Signal",
                    signal.StopLoss, signal.TargetPrice);

                if (result.IsSuccessful)
                {
                    Print($"Trade executed: {tradeType.Value} {volume} units at {Symbol.Bid}/{Symbol.Ask}");
                    Print($"Stop Loss: {signal.StopLoss}, Take Profit: {signal.TargetPrice}");
                }
                else
                {
                    Print($"Trade execution failed: {result.Error}");
                }
            }
            catch (Exception ex)
            {
                Print($"Error executing trade: {ex.Message}");
            }
        }

        /// <summary>
        /// Calculates position size based on risk percentage and stop loss
        /// </summary>
        private long CalculatePositionSize(TradingSignal signal)
        {
            try
            {
                var accountBalance = Account.Balance;
                var riskAmount = accountBalance * (RiskPercentage / 100.0);

                var currentPrice = signal.GetTradeType() == TradeType.Buy ? Symbol.Ask : Symbol.Bid;
                var stopDistance = Math.Abs(currentPrice - signal.StopLoss);

                if (stopDistance <= 0)
                {
                    Print("Invalid stop distance");
                    return 0;
                }

                var pipValue = Symbol.PipValue;
                var stopDistanceInPips = stopDistance / Symbol.PipSize;
                var riskPerPip = riskAmount / stopDistanceInPips;
                var volume = (long)(riskPerPip / pipValue);

                // Ensure volume is within symbol limits
                volume = Math.Max((long)Symbol.VolumeInUnitsMin, volume);
                volume = Math.Min((long)Symbol.VolumeInUnitsMax, volume);

                Print($"Risk Amount: {riskAmount:F2}, Stop Distance: {stopDistanceInPips:F1} pips, Volume: {volume}");

                return volume;
            }
            catch (Exception ex)
            {
                Print($"Error calculating position size: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Closes existing positions for the current symbol
        /// </summary>
        private void CloseExistingPositions(string reason)
        {
            var positions = Positions.FindAll("AgentServer Signal", SymbolName);

            foreach (var position in positions)
            {
                var result = ClosePosition(position);
                if (result.IsSuccessful)
                {
                    Print($"Closed position: {position.TradeType} {position.VolumeInUnits} units. Reason: {reason}");
                }
                else
                {
                    Print($"Failed to close position: {result.Error}");
                }
            }
        }
    }
}